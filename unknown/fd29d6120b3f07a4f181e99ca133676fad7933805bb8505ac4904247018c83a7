{"version": 3, "file": "require-array-sort-compare.js", "sourceRoot": "", "sources": ["../../src/rules/require-array-sort-compare.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,8CAAgC;AAShC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,4BAA4B;IAClC,cAAc,EAAE;QACd;YACE,kBAAkB,EAAE,KAAK;SAC1B;KACF;IAED,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,kEAAkE;YACpE,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,cAAc,EAAE,6BAA6B;SAC9C;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,kBAAkB,EAAE;wBAClB,WAAW,EACT,6DAA6D;wBAC/D,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;KACF;IAED,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAEjD;;;WAGG;QACH,SAAS,iBAAiB,CAAC,IAAyB;YAClD,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CACpC,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CACxC,CAAC;YACF,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;gBAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACtD,OAAO,QAAQ,CAAC,KAAK,CACnB,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,QAAQ,CACnD,CAAC;aACH;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,6FAA6F,CAC3F,MAAiC;gBAEjC,MAAM,MAAM,GAAG,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAChE,MAAM,aAAa,GAAG,IAAI,CAAC,4BAA4B,CACrD,OAAO,EACP,MAAM,CACP,CAAC;gBAEF,IAAI,OAAO,CAAC,kBAAkB,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;oBAClE,OAAO;iBACR;gBAED,IAAI,IAAI,CAAC,kCAAkC,CAAC,aAAa,EAAE,OAAO,CAAC,EAAE;oBACnE,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC;iBACvE;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}