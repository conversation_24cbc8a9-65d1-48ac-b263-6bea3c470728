{"lang": "pl", "rules": {"accesskeys": {"description": "War<PERSON>ść każdego atrybutu accessskey jest unikalna.", "help": "<PERSON><PERSON><PERSON>ć atrybutu accessskey musi być unikalna."}, "area-alt": {"description": "Elementy <area> w graficznych mapach odnośników mają tekst zastępczy.", "help": "Elementy aktywne <area> muszą mieć tekst alternatywny."}, "aria-allowed-attr": {"description": "Użyte atrybuty ARIA są dozwolone dla elementu z określoną rolą.", "help": "Elementy mogą używać tylko dozwolonych atrybutów ARIA."}, "aria-allowed-role": {"description": "Atrybut role ma odpowied<PERSON>ą wartość dla danego elementu.", "help": "<PERSON><PERSON> ARIA musi być odpowiednia dla danego elementu."}, "aria-braille-equivalent": {"description": "Upew<PERSON>j <PERSON>, że aria-braillelabel i aria-brailleroledescription mają odpowiednik niebrajlowski", "help": "Atrybuty aria-braille muszą mieć odpowiednik niebrajlowski."}, "aria-command-name": {"description": "Każdy element button, link i menuitem ARIA ma dostępną nazwę.", "help": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, pozy<PERSON><PERSON> muszą mieć dostępną nazwę."}, "aria-conditional-attr": {"description": "Upew<PERSON>j <PERSON>, że atrybuty ARIA są używane zgodnie ze specyfikacją roli elementu.", "help": "Atrybuty ARIA muszą być używane w sposób określony dla roli elementu"}, "aria-deprecated-role": {"description": "Upewnij się, że elementy nie używają przestarzałych ról.", "help": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> role ARIA nie mogą by<PERSON>ż<PERSON>."}, "aria-dialog-name": {"description": "Każde okno dialogowe ARIA i węzeł alertdialog ma dostępną nazwę.", "help": "Okno dialogowe ARIA i węzły alertdialog muszą mieć dostępną nazwę."}, "aria-hidden-body": {"description": "Element body nie ma atrybutu aria-hidden='true'.", "help": "Element body nie może mieć atrybutu aria-hidden='true'."}, "aria-hidden-focus": {"description": "Elementy z aria-hidden=true nie mogą obejmować elementów przyjmujących fokus.", "help": "Ukryty element ARIA nie może zawierać elementów przyjmujących fokus."}, "aria-input-field-name": {"description": "<PERSON><PERSON><PERSON> pole ARIA do wprowadzania danych ma dostępną nazwę.", "help": "Pola ARIA do wprowadzania danych muszą mieć dostępną nazwę."}, "aria-meter-name": {"description": "Każdy element ARIA meter (licznik) ma dostępną nazwę.", "help": "Liczniki ARIA (meter) muszą mieć dostępne nazwy."}, "aria-progressbar-name": {"description": "Każdy element ARIA progressbar (pasek postępu) ma dostępną nazwę.", "help": "<PERSON><PERSON> (progressbar) muszą mieć dostępne nazwy."}, "aria-prohibited-attr": {"description": "<PERSON><PERSON><PERSON><PERSON>, że atrybuty ARIA nie są zabronione dla roli elementu.", "help": "Elementy mogą używać tylko dozwolonych atrybutów ARIA."}, "aria-required-attr": {"description": "Elementy z rolą ARIA mają wszystkie wymagane atrybuty aria-*", "help": "Wymagane atrybuty ARIA muszą istnieć."}, "aria-required-children": {"description": "Elementy z atrybutem ARIA role, które muszą zawierać elementy potomne z wymaganym atrybutem role, z<PERSON><PERSON><PERSON><PERSON> je.", "help": "<PERSON><PERSON><PERSON><PERSON><PERSON> role ARIA m<PERSON><PERSON><PERSON> o<PERSON> okreś<PERSON> d<PERSON>."}, "aria-required-parent": {"description": "Elementy z atrybutem ARIA role, które wymagają elementu rodzica z atrybutem role, s<PERSON> zawarte w elementach z takimi rolami.", "help": "Elementy z niektórymi atrybutami role ARIA muszą znajdować się wewnątrz nadrzędnego elementu rodzica z wymaganym atrybutem role."}, "aria-roledescription": {"description": "Atrybut aria-roledescription jest używany tylko w elementach, które mają rolę określoną domyślnie lub jawnie.", "help": "Użyj aria-roledescription w elementach o roli semantycznej."}, "aria-roles": {"description": "<PERSON><PERSON><PERSON><PERSON> atrybutu role s<PERSON> poprawne.", "help": "<PERSON><PERSON><PERSON><PERSON> role ARIA mus<PERSON><PERSON> mieć poprawne <PERSON>."}, "aria-text": {"description": "Atrybut role=\"text\" jest u<PERSON><PERSON><PERSON><PERSON> d<PERSON>ów, które nie mają potomków przyjmujących fokus", "help": "Element z atrybutem role=\"text\" nie może mieć potomków przyjmujących fokus."}, "aria-toggle-field-name": {"description": "Każdy element ARIA toggle (przełącznik) ma dostępną nazwę.", "help": "Przełączniki ARIA  (toggle) muszą mieć dostępną nazwę."}, "aria-tooltip-name": {"description": "Każdy element ARIA tooltip (podpowiedź) ma dostępną nazwę.", "help": "Podpowied<PERSON> ARIA (tooltip) muszą mieć dostępną nazwę."}, "aria-treeitem-name": {"description": "Każdy element ARIA treeitem (węzeł drzewa) ma dostępną nazwę.", "help": "Węzły drzewa elementów ARIA (treeitem) muszą mieć dostępną nazwę."}, "aria-valid-attr-value": {"description": "Wszystkie atrybuty ARIA mają poprawne wartoś<PERSON>.", "help": "Atrybuty ARIA muszą mieć poprawne war<PERSON>."}, "aria-valid-attr": {"description": "Wszystkie atrybuty aria-* mają poprawne nazwy.", "help": "Atrybuty ARIA muszą mieć poprawne nazwy."}, "audio-caption": {"description": "Elementy <audio> maj<PERSON> napisy rozszerzone.", "help": "Elementy <audio> muszą mieć ścieżkę z napisami."}, "autocomplete-valid": {"description": "Pol<PERSON>, kt<PERSON><PERSON> zbieraj<PERSON> da<PERSON>, mają poprawne atrybuty autocomplete", "help": "Atrybut autocomplete musi być użyty poprawnie."}, "avoid-inline-spacing": {"description": "Odstępy w tekście można regulować za pomocą własnych arkuszy stylów.", "help": "Odstępy w tekście muszą być regulowane za pomocą własnych arkuszy stylów."}, "blink": {"description": "Elementy <blink> nie są <PERSON>.", "help": "Elementy <blink> są przestarzałe i nie mogą być używane."}, "button-name": {"description": "Każdy przycisk ma odróżniającą go dostępną nazwę.", "help": "Przyciski muszą mieć odróżniający je tekst nazwy."}, "bypass": {"description": "Każda strona ma co najmniej jeden mechanizm, który pozwala ominąć nawigację i przejść od razu do treści.", "help": "Strona musi mieć środki do ominięcia powtarzających bloków treści."}, "color-contrast-enhanced": {"description": "Kontrast między kolorami pierwszego planu i tła spełnia wyższe progi współczynnika kontrastu WCAG 2 AAA.", "help": "Elementy muszą spełniać wyższe progi współczynnika kontrastu kolorów"}, "color-contrast": {"description": "Kontrast między kolorami pierwszego planu i tła spełnia progi kontrastu WCAG 2 AA.", "help": "Elementy muszą mieć wystarczający kontrast kolorów."}, "css-orientation-lock": {"description": "<PERSON><PERSON><PERSON><PERSON> nie jest przypisana do żadnej konkretnej orientacji wyświetlacza i można ją obsługiwać we wszystkich orientacjach wyświetlacza.", "help": "Zapytania medialne nie są wykorzystywane do blokowania orientacji wyświetlacza."}, "definition-list": {"description": "Elementy <dl> mają poprawną strukturę.", "help": "Elementy <dl> m<PERSON>ą bezpośrednio z<PERSON> tylko odpowiednio uporządkowane grupy <dt> i <dd> oraz elementy <script>, <template> lub <div>."}, "dlitem": {"description": "Elementy <dt> i <dd> znaj<PERSON><PERSON><PERSON> się bezpośrednio w <dl>.", "help": "Elementy <dt> i <dd> są wewnątrz elementu <dl>."}, "document-title": {"description": "Ka<PERSON>dy dokument HTML ma niepusty element <title>.", "help": "Dokumenty muszą mieć element <title> pomagający w nawigacji."}, "duplicate-id-active": {"description": "Wartość każdego atrybutu id aktywnych elementów jest unikalna.", "help": "ID aktywnych elementów, które otrzymują fokus, muszą być unikalne."}, "duplicate-id-aria": {"description": "Każdy atrybut id używany w ARIA i w etykietach jest unikalny.", "help": "Identyfikatory s<PERSON>owane w ARIA i etykietach muszą być unikalne."}, "duplicate-id": {"description": "Wartość każdego atrybutu id jest unikalna.", "help": "Wartość atrybutu id musi być unikalna."}, "empty-heading": {"description": "Nagłówki mają odróżniający je tekst.", "help": "Nagłówki nie mogą być puste."}, "empty-table-header": {"description": "<PERSON>ew<PERSON>j <PERSON>, że nagłówki tabel mają opisowy tekst.", "help": "Tekst nagłówka tabeli nie może być pusty."}, "focus-order-semantics": {"description": "Elementy w porządku otrzymywania fokusu mają odpowiednią rolę.", "help": "Elementy w porządku otrzymywania fokusu muszą mieć rolę odpowiednią dla treści interaktywnych."}, "form-field-multiple-labels": {"description": "Żadne pole formularza nie ma wielu etykiet (elementów label).", "help": "Pole formularza nie może mieć wielu elementów label."}, "frame-focusable-content": {"description": "Elementy <frame> i <iframe> z treścią przyjmującą fokus nie mają tabindex=-1.", "help": "Ramki z treścią przyjmującą fokus nie mogą mieć tabindex=-1."}, "frame-tested": {"description": "Elementy <iframe> i <frame> muszą być testowane ze skryptem axe-core.", "help": "Ram<PERSON> m<PERSON>z<PERSON> być testowane ze skryptem axe-core."}, "frame-title-unique": {"description": "Elementy <iframe> i <frame> mają unikalny atrybut title.", "help": "Ram<PERSON> muszą mieć unikalny atrybut title."}, "frame-title": {"description": "Elementy <iframe> i <frame> mają niepusty atrybut title.", "help": "Ram<PERSON> muszą mieć niepusty atrybut title."}, "heading-order": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nagłówków jest semantycznie poprawna.", "help": "Poziomy nagłówków powinny wzrastać tylko o jeden."}, "hidden-content": {"description": "Na stronie są ukryte treści.", "help": "Ukrytych treści na stronie nie można analizować."}, "html-has-lang": {"description": "Każdy dokument HTML ma atrybut lang.", "help": "Element <html> musi mieć atrybut lang."}, "html-lang-valid": {"description": "Atrybut lang elementu <html> ma poprawn<PERSON> war<PERSON>.", "help": "Element <html> musi mieć poprawną wartość atrybutu lang."}, "html-xml-lang-mismatch": {"description": "Element HTML z poprawnym atrybutem lang xml:lang ma ten sam podstawowy język strony.", "help": "Element HTML z lang i xml:lang musi mieć ten sam język podstawowy."}, "identical-links-same-purpose": {"description": "Łącza o tej samej dostępnej nazwie służą temu samemu celowi.", "help": "Łącza o tej samej nazwie mają ten sam cel."}, "image-alt": {"description": "Elementy <img> maj<PERSON> atrybut alt lub rolę none albo presentation.", "help": "<PERSON><PERSON>zy muszą mieć tekst alternatywny."}, "image-redundant-alt": {"description": "Tekst alternatywny obrazu nie jest powtarzany w tekście.", "help": "Tekst alternatywny obrazów nie powinien być powtarzany w tekście."}, "input-button-name": {"description": "Przyciski input type=button mają odróżniający je tekst.", "help": "Przyciski input type=button muszą mieć odróżniający je tekst."}, "input-image-alt": {"description": "Elementy <input type=\"image\"> mają tekst alternatywny.", "help": "Przyciski graficzne muszą mieć tekst alternatywny."}, "label-content-name-mismatch": {"description": "Elementy oznakowane swoją treścią mają swój widoczny tekst jako czę<PERSON> ich dostępnej nazwy", "help": "Elementy muszą mieć swój widoczny tekst jako cz<PERSON> ich dostępnej nazwy."}, "label-title-only": {"description": "Żaden element formularza nie jest oznaczony wyłącznie za pomocą atrybutu title lub aria-describedby.", "help": "Element formularza powinien mieć widoczną etykietę."}, "label": {"description": "Każdy element formularza ma etykietę.", "help": "Element formularza musi mieć etykietę."}, "landmark-banner-is-top-level": {"description": "Obszar banner jest obszarem kluczowym najwyższego poziomu.", "help": "Punkt orientacyjny banner nie może być zawarty wewnątrz innego obszaru kluczowego."}, "landmark-complementary-is-top-level": {"description": "Obs<PERSON>y aside lub z role=complementary są obszarami kluczowymi najwyższego poziomu.", "help": "Punkt orientacyjny complementary nie może być zawarty wewnątrz innego obszaru kluczowego."}, "landmark-contentinfo-is-top-level": {"description": "O<PERSON><PERSON> klu<PERSON>owy z role=contentinfo jest obszarem kluczowym najwyższego poziomu.", "help": "Punkt orientacyjny contentinfo nie może być zawarty wewnątrz innego obszaru kluczowego."}, "landmark-main-is-top-level": {"description": "Obszar main jest obszarem kluczowym najwyższego poziomu.", "help": "Punkt orientacyjny main nie może być zawarty wewnątrz innego obszaru kluczowego."}, "landmark-no-duplicate-banner": {"description": "Dokument ma co najwyżej jeden punkt orientacyjny banner.", "help": "Dokument nie może mieć więcej niż jednego obszaru kluczowego banner."}, "landmark-no-duplicate-contentinfo": {"description": "Dokument ma co najwyżej jeden punkt orientacyjny contentinfo.", "help": "Dokument nie może mieć więcej niż jednego obszaru kluczowego contentinfo."}, "landmark-no-duplicate-main": {"description": "Dokument ma co najwyżej jeden punkt orientacyjny main.", "help": "Dokument nie może mieć więcej niż jednego obszaru kluczowego main."}, "landmark-one-main": {"description": "Dokument ma punkt orientacyjny main.", "help": "Dokument może mieć tylko jeden obszar kluczowy main."}, "landmark-unique": {"help": "Punkty orientacyjne są unikalne", "description": "Punkty orientacyjne (obszary kluczowe) mają unikalną rolę lub kombinację roli/etykiety/tytułu (tj. dostępną nazwę)."}, "link-in-text-block": {"description": "Łącza można rozróżniać bez opierania się na kolorze.", "help": "Łącza muszą być odróżnialne od sąsiadującego tekstu w sposób, kt<PERSON>ry nie opiera się na kolorze."}, "link-name": {"description": "Łącza mają odróżniający je tekst.", "help": "Łącza muszą mieć odróżniający je tekst."}, "list": {"description": "Listy mają poprawną strukturę.", "help": "Elementy <ul> i <ol> mogą bezpośrednio z<PERSON> tylko elementy <li>, <script> lub <template>."}, "listitem": {"description": "Elementy <li> są używane semantycznie.", "help": " Elementy <li> musz<PERSON> by<PERSON> zawarte bezpośrednio w <ul> lub <ol>."}, "marquee": {"description": "Elementy <marquee> nie są uż<PERSON>.", "help": "Elementy <marquee> są przestarzałe i nie mogą być używane."}, "meta-refresh-no-exceptions": {"description": "Upewnij się, że <meta http-equiv=\"refresh\"> nie jest używany do opóźnionego odświeżania", "help": "<PERSON><PERSON> w<PERSON><PERSON> s<PERSON> opóźnionego odświeżania"}, "meta-refresh": {"description": "Znacznik <meta http-equiv=\"refresh\"> nie jest używany do opóźnionego odświeżania.", "help": "Opóźnione odświeżanie poniżej 20 godzin nie może by<PERSON> s<PERSON>."}, "meta-viewport-large": {"description": "Element <meta name=\"viewport\"> umożliwia znaczne powiększanie.", "help": "Użytkownicy mogą powiększać i skalować tekst do 500%."}, "meta-viewport": {"description": "Element <meta name=\"viewport\"> nie wyłącza skalowania i powiększania tekstu.", "help": "Powiększanie i skalowanie nie może być wyłączone."}, "nested-interactive": {"description": "<PERSON><PERSON><PERSON><PERSON>, że kontrolki interaktywne nie są zagnieżdżone, ponieważ nie zawsze są ogłaszane przez czytniki ekranu lub mogą powodować problemy technologii wspomagających z fokusem.", "help": "Kontrolki interaktywne nie mogą być zagnieżdżone"}, "no-autoplay-audio": {"description": "Elementy <video> lub <audio> nie odtwarzają automatycznie dźwięku przez dłużej niż 3 sekundy bez mechanizmu, kt<PERSON><PERSON> go zatrzymuje lub wycisza.", "help": "Elementy <video> lub <audio> nie odtwarzają dźwięku automatycznie."}, "object-alt": {"description": "Elementy <object> mają tekst zastępczy.", "help": "Elementy <object> muszą mieć alternatywę tekstową."}, "p-as-heading": {"description": "Pogrubienie, kursywa i rozmiar czcionki nie są używane do stylizacji elementów <p> jako nagłówk<PERSON>.", "help": "Stylizowane elementy <p> nie mogą być używane jako nagłów<PERSON>."}, "page-has-heading-one": {"description": "Strona lub co najmniej jedna z jej ramek, zawiera nagłówek pierwszego poziomu.", "help": "Strona musi zawierać nagłówek poziomu 1."}, "presentation-role-conflict": {"description": "Elementy oznaczone jako prezentacyjne nie powinny mieć globalnego ARIA ani tabin<PERSON>, <PERSON><PERSON>, że wszystkie czytniki ekranu je zignorują.", "help": "<PERSON><PERSON><PERSON><PERSON>, że elementy oznaczone jako prezentacyjne są konsekwentnie ignorowane."}, "region": {"description": "Cała treść strony jest objęta przez punkty orientacyjne.", "help": "Cała treść strony musi być zawarta w obszarach kluczowych."}, "role-img-alt": {"description": "Elementy z [role='img'] mają tekst alternatywny.", "help": "Elementy z [role='img'] muszą mieć tekst alternatywny."}, "scope-attr-valid": {"description": "Atrybut scope w tabelach jest stosowany poprawnie.", "help": "Atrybut scope ma poprawną wartość."}, "scrollable-region-focusable": {"description": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> tre<PERSON> mo<PERSON> przew<PERSON>, są osiągalne za pomocą klawiatury.", "help": "Obszary przewijane muszą być osiągalne z klawiatury."}, "select-name": {"description": "Element select ma dostępną nazwę.", "help": "Element select musi mieć dostępną nazwę."}, "server-side-image-map": {"description": "Graficzne mapy odnośników (mapy obrazkowe) obsługiwane po stronie serwera nie są używane.", "help": "Nie wolno używać map odnośników po stronie serwera."}, "skip-link": {"description": "Wszystkie łącza pomijania mają cel przyjmujący fokus.", "help": "Cel łącza pomijającego powinien istnieć i przyjmować fokus."}, "svg-img-alt": {"description": "Elementy <svg> z rolą img, graphics-document lub graphics-symbol mają dostępny tekst.", "help": "Elementy svg z rolą img muszą mieć tekst alternatywny."}, "tabindex": {"description": "Wartości atrybutów tabindex nie są większe niż 0.", "help": "Elementy nie powinny mieć wartości tabindex większej niż zero."}, "table-duplicate-name": {"description": "<PERSON><PERSON><PERSON> tabeli (element <caption>) nie może zawierać takiego samego tekstu co atrybut summary.", "help": "Atrybut summary w tabeli ma inny tekst niż element caption."}, "table-fake-caption": {"description": "Tabele używają jako podpisu elementu <caption>.", "help": "Komórki danych i nagłówkowe w tabeli danych nie są używane do umieszczania podpisu tabeli."}, "target-size": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy cel dotykowy ma wystarczający rozmiar i przestrzeń wokół.", "help": "Wszystkie cele dotykowe muszą mieć rozmiar 24px lub pozostaw<PERSON>ć wystarczają<PERSON>ą ilość miejsca wokół."}, "td-has-header": {"description": "Wszystkie niepuste komórki danych w tabelach danych większych niż 3 na 3 mają jeden lub więcej nagłówków tabeli.", "help": "Ka<PERSON>dy niepusty element <td> w dużej tabeli musi mieć powiązany nagłówek tabeli"}, "td-headers-attr": {"description": "Każda komórka tabeli używająca atrybutu headers odwołuje się do innej komórki w tej tabeli.", "help": "Wszystkie komórki z atrybutem headers odnoszą się tylko do innych komórek tej samej tabeli."}, "th-has-data-cells": {"description": "Wszystkie elementy th i elementy z role=columnheader/rowheader mają komórki danych, które opisują.", "help": "Każdy nagłówek tabeli w tabeli danych musi odnosić się do komórek danych."}, "valid-lang": {"description": "Atrybuty lang mają poprawne <PERSON>.", "help": "Atrybuty lang muszą mieć poprawną wartość."}, "video-caption": {"description": "Elementy <video> maj<PERSON> napisy rozszerzone.", "help": "Elementy <video> muszą mieć napisy rozszerzone."}}, "checks": {"abstractrole": {"pass": "Role abstrakcyjne nie są wykorzystywane.", "fail": {"singular": "Rola abstrakcyjna nie może być użyta bezpośrednio: ${data.values}.", "plural": ": Role abstrakcyjne nie mogą być używane bezpośrednio: ${data.values}."}}, "aria-allowed-attr": {"pass": "Atrybuty ARIA są używane poprawnie dla zdefiniowanej roli.", "fail": {"singular": "Atrybut ARIA nie jest dozwolony: ${data.values}.", "plural": ": Atrybuty ARIA nie są dozwolone: ${data.values}."}, "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy nie ma <PERSON>, je<PERSON><PERSON> atrybut ARIA jest ignorowany w tym elemencie: ${data.values}"}, "aria-allowed-role": {"pass": "R<PERSON> jest dozwolona dla danego elementu.", "fail": {"singular": "Rola ARIA ${data.values} nie jest dozwolona dla tego elementu.", "plural": ": Role ARIA ${data.values} nie są dozwolone dla tego elementu."}, "incomplete": {"singular": "Rola ARIA ${data.values} musi by<PERSON>, gdy element jest wido<PERSON>ny, poniew<PERSON>ż nie jest to dozwolone dla elementu.", "plural": ": Role ARIA ${data.values} muszą by<PERSON>, gdy element jest wido<PERSON>, ponieważ nie są one dozwolone dla elementu."}}, "aria-busy": {"pass": "Element ma atrybut aria-busy", "fail": "Element używa aria-busy=\"true\" podczas pokazywania ładowania"}, "aria-conditional-attr": {"pass": "Atrybut ARIA jest dozwolony", "fail": {"checkbox": "<PERSON><PERSON><PERSON> aria-checked lub ustaw jego warto<PERSON> na \"${data.checkState}\", aby dopasować ją do rzeczywistego stanu pola wyboru.", "rowSingular": "Ten atrybut jest obsługiwany przez wiersze siatki, ale nie przez ${data.ownerRole}: ${data.invalidAttrs}", "rowPlural": "Te atrybuty są obsługiwane przez wiersze siatki, ale nie przez ${data.ownerRole}: ${data.invalidAttrs}"}}, "aria-errormessage": {"pass": "Istnieje aria-errormessage oraz elementy referencyjne widoczne dla czytników ekranowych, które wykorzystują wspieraną technikę aria-errormessage.", "fail": {"singular": "Wartość aria-errormessage ${data.values} musi używać techniki ogłoszenia wiadomości (np. aria-live, aria-describedby, role=alert, etc.).", "plural": "Wartości aria-errormessage ${data.values} muszą używać techniki ogłoszenia wiadomości (np. aria-live, aria-describedby, role=alert, etc.).", "hidden": "Wartość aria-errormessage ${data.values} nie może odwoływać się do ukrytego elementu."}, "incomplete": {"singular": "<PERSON><PERSON><PERSON><PERSON> się, <PERSON><PERSON> war<PERSON> aria-errormessage ${data.values} odnosi się do istniejącego elementu.", "plural": "Upew<PERSON>j się, że wartoś<PERSON> aria-errormessage ${data.values} odnoszą się do istniejących elementów.", "idrefs": "<PERSON><PERSON> m<PERSON>, czy na stronie istnieje element aria-errormessage: ${data.values}"}}, "aria-hidden-body": {"pass": "<PERSON>e ma <PERSON>adnego atrybutu aria-hidden w elemencie body dokumentu.", "fail": "Atrybut aria-hidden=true nie może być użyty w elemencie body dokumentu."}, "aria-level": {"pass": "<PERSON><PERSON>ść aria-level jest poprawna", "incomplete": "Wartości aria-level większe niż 6 nie są obsługiwane we wszystkich kombinacjach czytników ekranu i przeglądarek"}, "aria-prohibited-attr": {"pass": "Atrybut ARIA jest dozwolony", "fail": {"hasRolePlural": "Atrybuty ${data.prohibited} nie mogą być używane z rolą \"${data.role}\".", "hasRoleSingular": "Atrybut ${data.prohibited} nie może być użyty z rolą \"${data.role}\".", "noRolePlural": "Atrybuty ${data.prohibited} nie mogą być używane w ${data.nodeName} bez poprawnego atrybutu roli.", "noRoleSingular": "Atrybut ${data.prohibited} nie może być użyty w ${data.nodeName} bez poprawnego atrybutu roli."}, "incomplete": {"hasRoleSingular": "Atrybut ${data.prohibited} nie jest dobrze obsługiwany przez rolę \"${data.role}\".", "hasRolePlural": "Atrybuty ${data.prohibited} nie są dobrze obsługiwane przez role \"${data.role}\".", "noRoleSingular": "Atrybut ${data.prohibited} nie jest dobrze obsługiwany przez ${data.nodeName} bez poprawnego atrybutu roli.", "noRolePlural": "Atrybuty ${data.prohibited} nie są dobrze obsługiwane przez ${data.nodeName} bez poprawnego atrybutu roli."}}, "aria-required-attr": {"pass": "Wszystkie wymagane atrybuty ARIA istnieją.", "fail": {"singular": "Wymagany atrybut ARIA nie istnieje: ${data.values}.", "plural": "Wymagane atrybuty ARIA nie istnieją: ${data.values}."}}, "aria-required-children": {"pass": {"default": "Wymagane dzieci ARIA istnieją."}, "fail": {"singular": "Wymagana rola dziecka ARIA nie istnieje: ${data.values}.", "plural": "<PERSON><PERSON><PERSON><PERSON> role dzieci ARIA nie istnieją: ${data.values", "unallowed": "Element ma d<PERSON>ci, które nie są dozwolone: ${data.values}"}, "incomplete": {"singular": "<PERSON><PERSON><PERSON><PERSON> dodać oczekiwaną rolę dziecka ARIA: ${data.values}.", "plural": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> role dzieci ARIA: ${data.values}."}}, "aria-required-parent": {"pass": "Wymagana rola rodzica ARIA istnieje.", "fail": {"singular": "Wymagana rola rodzica ARIA nie istnieje: ${data.values}.", "plural": "<PERSON><PERSON><PERSON><PERSON> role rodziców ARIA nie istnieją: ${data.values}."}}, "aria-roledescription": {"pass": "Atrybut aria-roledescription jest używany z obsługiwaną rolą semantyczną.", "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy aria-roledescription jest ogłaszany przez obsługiwane czytniki ekranu.", "fail": "Na<PERSON>j temu elementowi rolę, która obsługuje aria-roledescription."}, "aria-unsupported-attr": {"pass": "Atrybut ARIA jest obsługiwany.", "fail": "Atrybut ARIA nie jest dostatecznie obsługiwany przez czytniki ekranu i inne technologie wspomagające: ${data.values}."}, "aria-valid-attr-value": {"pass": "Wartości atrybutu ARIA są poprawne.", "fail": {"singular": "Niep<PERSON><PERSON><PERSON> war<PERSON> atrybutu ARIA: ${data.values}", "plural": "Niepoprawne wartości atrybutu ARIA: ${data.values}"}, "incomplete": {"noId": "Identyfikator elementu atrybutu ARIA nie istnieje na stronie: ${data.needsReview}", "noIdShadow": "ID elementu atrybutu ARIA nie istnieje na stronie lub jest potomkiem innego drzewa shadow DOM: ${data.needsReview}", "ariaCurrent": "<PERSON><PERSON>ść atrybutu ARIA jest niepoprawna i będzie traktowana jako \"aria-current=true\": ${data.needsReview}", "idrefs": "<PERSON><PERSON> m<PERSON>, czy atrybut ARIA element ID istnieje na stronie: ${data.needsReview}", "empty": "<PERSON><PERSON><PERSON><PERSON> atrybutu ARIA jest i<PERSON><PERSON>ana, gdy jest pusty: ${data.needsReview}"}}, "aria-valid-attr": {"pass": "Nazwa atrybutu ARIA jest poprawna.", "fail": {"singular": "Niepoprawna nazwa atrybutu ARIA: ${data.values}.", "plural": "Niepoprawne nazwy atrybutów ARIA: ${data.values}."}}, "braille-label-equivalent": {"pass": "Atrybut aria-braillelabel jest użyty w elemencie z dostępnym tekstem.", "fail": "Atrybut aria-braillelabel jest użyty w elemencie, kt<PERSON>ry nie ma dostępnego tekstu.", "incomplete": "<PERSON>e można wyliczyć dostępnego tekstu."}, "braille-roledescription-equivalent": {"pass": "Atrybut aria-brailleroledescription nie jest używany w elemencie, który nie ma dostępnego tekstu.", "fail": {"noRoleDescription": "Atrybut aria-brailleroledescription jest użyty w elemencie bez atrybutu aria-roledescription.", "emptyRoleDescription": "Atrybut aria-brailleroledescription jest użyty w elemencie z pustym atrybutem aria-roledescription."}}, "deprecatedrole": {"pass": "Rola ARIA nie jest przestarzała", "fail": "Użyta rola jest przestarzała: ${data}"}, "fallbackrole": {"pass": "Użyto tylko jednej wartości roli.", "fail": "Uż<PERSON>j tylko jednej war<PERSON>ci roli, p<PERSON><PERSON><PERSON><PERSON> <PERSON> rezerwowe nie są obsługiwane w starszych przeglądarkach.", "incomplete": "Używaj tylko roli 'presentation' lub 'none', poniew<PERSON>ż są one synonimami."}, "has-global-aria-attribute": {"pass": {"singular": "Element ma ogólny atrybut ARIA: ${data.values}.", "plural": ": Element ma ogólne atrybuty ARIA: ${data.values}."}, "fail": "Element nie ma ogólnego atrybutu ARIA"}, "has-widget-role": {"pass": "Element ma rolę widżetu.", "fail": "Element nie ma roli widżetu."}, "invalidrole": {"pass": "<PERSON><PERSON> jest poprawna.", "fail": {"singular": "Rola musi być jedną z poprawnych ról ARIA: ${data.values}.", "plural": ": Role muszą być jedną z poprawnych ról ARIA: ${data.values}."}}, "is-element-focusable": {"pass": "Element przyjmuje fokus.", "fail": "Element nie przyjmuje fokusu."}, "no-implicit-explicit-label": {"pass": "Nie ma rozbieżności między label a dostępną nazwą.", "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy label nie musi być częścią nazwy pola ARIA ${data}."}, "unsupportedrole": {"pass": "Rola ARIA jest obsługiwana.", "fail": "Zastosowana rola nie jest szeroko obsługiwana w czytnikach ekranu i technologiach wspomagających: ${data.values}."}, "valid-scrollable-semantics": {"pass": "Element w porządku otrzymywania fokusu ma poprawną semantykę.", "fail": "Element w porządku otrzymywania fokusu ma niepoprawną semantykę."}, "color-contrast-enhanced": {"pass": "Element ma wystarczający kontrast kolorów ${data.contrastRatio}", "fail": {"default": "Element ma niewystarczający kontrast kolorów ${data.contrastRatio} (kolor pierwszego planu: ${data.fgColor}, kolor tła: ${data.bgColor}, rozmiar czcionki: ${data.fontSize}, waga czcionki: ${data.fontWeight}). Oczekiwany współczynnik kontrastu: ${data.expectedContrastRatio}", "fgOnShadowColor": "Element ma niewystarczający kontrast kolorów ${data.contrastRatio} pomiędzy kolorem pierwszego planu a kolorem cienia tekstu (kolor pierwszego planu: ${data.fgColor}, kolor cienia tekstu: ${data.shadowColor}, rozmiar czcionki: ${data.fontSize}, waga czcionki: ${data.fontWeight}). Oczekiwany współczynnik kontrastu: ${data.expectedContrastRatio}", "shadowOnBgColor": "Element ma niewystarczający kontrast kolorów ${data.contrastRatio} pomiędzy kolorem cienia tekstu a kolorem tła (kolor cienia tekstu: ${data.shadowColor}, kolor tła: ${data.bgColor}, rozmiar czcionki: ${data.fontSize}, waga czcionki: ${data.fontWeight}). Oczekiwany współczynnik kontrastu: ${data.expectedContrastRatio}"}, "incomplete": {"default": "Nie można okreś<PERSON>ć współczynnika kontrastu.", "bgImage": "<PERSON>e można określić koloru tła elementu, ponieważ element ma obraz tła.", "bgGradient": "<PERSON>e można określić koloru tła elementu, ponieważ element ma gradientowe tło.", "imgNode": "<PERSON>e można określić koloru tła elementu, ponieważ element zawiera węzeł obrazu.", "bgOverlap": "Nie można określić koloru tła elementu, poni<PERSON><PERSON>ż nakłada się na niego inny element.", "fgAlpha": "Nie można określić koloru pierwszego planu elementu z powodu przezroczystości alfa.", "elmPartiallyObscured": "<PERSON>e można określić koloru tła elementu, poni<PERSON><PERSON><PERSON> jest on częściowo zasłonięty przez inny element.", "elmPartiallyObscuring": "<PERSON>e można określić koloru tła elementu, poni<PERSON><PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON>wo nakłada się on na inne elementy.", "outsideViewport": "<PERSON>e można określić koloru tła elementu, poniew<PERSON>ż znajduje się on poza obszarem operacyjnym.", "equalRatio": "Element ma współczynnik kontrastu 1:1 z tłem.", "shortTextContent": "<PERSON><PERSON><PERSON>ć elementu jest zbyt k<PERSON>, aby <PERSON><PERSON><PERSON><PERSON>, czy jest to rzeczywista treść tekstowa.", "nonBmp": "Treść elementu zawiera tylko znaki nietekstowe.", "pseudoContent": "<PERSON><PERSON> można okreś<PERSON> koloru tła elementu, poni<PERSON><PERSON><PERSON> jest to pseudoelement."}}, "color-contrast": {"pass": {"default": "Element ma wystarczający kontrast kolorów  ${data.contrastRatio}", "hidden": "Element jest ukryty"}, "fail": {"default": "Element ma niewystarczający kontrast kolorów ${data.contrastRatio} (kolor pierwszego planu: ${data.fgColor}, kolor tła: ${data.bgColor}, rozmiar czcionki: ${data.fontSize}, waga czcionki: ${data.fontWeight}). Oczekiwany współczynnik kontrastu: ${data.expectedContrastRatio}", "fgOnShadowColor": "Element ma niewystarczający kontrast kolorów ${data.contrastRatio} pomiędzy kolorem pierwszego planu a kolorem cienia tekstu (kolor pierwszego planu: ${data.fgColor}, kolor cienia tekstu: ${data.shadowColor}, rozmiar czcionki: ${data.fontSize}, waga czcionki: ${data.fontWeight}). Oczekiwany współczynnik kontrastu: ${data.expectedContrastRatio}", "shadowOnBgColor": "Element ma niewystarczający kontrast kolorów ${data.contrastRatio} pomiędzy kolorem  cienia tekstu a kolorem tła (kolor cienia tekstu: ${data.shadowColor}, kolor tła: ${data.bgColor}, rozmiar czcionki: ${data.fontSize}, waga czcionki: ${data.fontWeight}). Oczekiwany współczynnik kontrastu: ${data.expectedContrastRatio}"}, "incomplete": {"default": "Nie można okreś<PERSON>ć współczynnika kontrastu.", "bgImage": "<PERSON>e można określić koloru tła elementu, ponieważ element ma obraz tła.", "bgGradient": "<PERSON>e można określić koloru tła elementu, ponieważ element ma gradientowe tło.", "imgNode": "<PERSON>e można określić koloru tła elementu, ponieważ element zawiera węzeł obrazu.", "bgOverlap": "Nie można określić koloru tła elementu, poni<PERSON><PERSON>ż nakłada się na niego inny element.", "fgAlpha": "Nie można określić koloru pierwszego planu elementu z powodu przezroczystości alfa.", "elmPartiallyObscured": "<PERSON>e można określić koloru tła elementu, poni<PERSON><PERSON><PERSON> jest on częściowo zasłonięty przez inny element.", "elmPartiallyObscuring": "<PERSON>e można określić koloru tła elementu, poni<PERSON><PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON>wo nakłada się on na inne elementy.", "outsideViewport": "<PERSON>e można określić koloru tła elementu, poniew<PERSON>ż znajduje się on poza obszarem operacyjnym.", "equalRatio": "Element ma współczynnik kontrastu 1:1 z tłem.", "shortTextContent": "<PERSON><PERSON><PERSON>ć elementu jest zbyt k<PERSON>, aby <PERSON><PERSON><PERSON><PERSON>, czy jest to rzeczywista treść tekstowa.", "nonBmp": "Treść elementu zawiera tylko znaki nietekstowe.", "pseudoContent": "<PERSON><PERSON> można okreś<PERSON> koloru tła elementu, poni<PERSON><PERSON><PERSON> jest to pseudoelement."}}, "link-in-text-block-style": {"pass": "Łącza mogą być odróżnione od sąsiadującego tekstu poprzez wizualną stylizację", "incomplete": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy łącze wymaga stylizacji, a<PERSON> o<PERSON><PERSON><PERSON><PERSON> je od sąsiadującego tekstu.", "pseudoContent": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy pseudostyl łącza jest wys<PERSON><PERSON><PERSON><PERSON>, a<PERSON> o<PERSON><PERSON><PERSON><PERSON> je od sąsiadującego tekstu."}, "fail": "Łącze nie ma żadnej sty<PERSON>zacji (np. podkreślenia), która odróżniałaby je od sąsiadującego tekstu"}, "link-in-text-block": {"pass": "Łącza można odróżnić od sąsiadującego tekstu w inny sposób niż za pomocą koloru.", "fail": {"fgContrast": "Łącze ma niewystarczający kontrast kolorów ${data.contrastRatio}:1 z sąsiadującym tekstem. (Minimalny kontrast to: ${data.requiredContrastRatio}:1, kolor tekstu łącza: ${data.nodeColor}, kolor sąsiadującego tekstu: ${data.parentColor})", "bgContrast": "Tło łącza ma niewystarczający kontrast kolorów ${data.contrastRatio} (Minimalny kontrast to:  ${data.requiredContrastRatio}:1, kolor tła łącza: ${data.nodeBackgroundColor}, sąsiadujący kolor tła: ${data.parentBackgroundColor})"}, "incomplete": {"default": "Nie można okreś<PERSON>ć współczynnika kontrastu.", "bgContrast": "Nie można określić współczynnika kontrastu elementu. Sprawdź, czy nie ma odrębnego stylu stanów hover/fokus.", "bgImage": "Nie można określić współczynnika kontrastu elementu ze względu na obraz tła.", "bgGradient": "Nie można określić współczynnika kontrastu elementu ze względu na gradient tła.", "imgNode": "Nie można określić współczynnika kontrastu elementu, ponieważ element zawiera węzeł obrazu.", "bgOverlap": "Nie można określić współczynnika kontrastu elementu ze względu na nakładanie się elementów."}}, "autocomplete-appropriate": {"pass": "<PERSON><PERSON>ść autocomplete jest odpowiednia dla tego typu pola formularza.", "fail": "<PERSON><PERSON>ść autocomplete jest niewłaściwa dla tego typu pola formularza."}, "autocomplete-valid": {"pass": "Atrybut autocomplete jest sformatowany poprawnie.", "fail": "Atrybut autocomplete jest sformato<PERSON>y niep<PERSON><PERSON>nie."}, "accesskeys": {"pass": "<PERSON><PERSON><PERSON><PERSON> atrybutu accesskey jest unikalna.", "fail": "Dokument ma wiele elementów z tym samym klawiszem dostępu."}, "focusable-content": {"pass": "Element zawiera elementy przyjmujące fokus.", "fail": "Element powinien przyjmować fokus."}, "focusable-disabled": {"pass": "W elemencie nie ma elementów przyjmujących fokus.", "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, na których można ustawić fokus, natychmiast otrzymują wskaźnik fokusu", "fail": "Treść z możliwością ustawiania fokusu powinna być wyłączona lub usunięta z DOM."}, "focusable-element": {"pass": "Element może przyjmować fokus.", "fail": "Element powinien przyjmować fokus."}, "focusable-modal-open": {"pass": "<PERSON><PERSON> ma <PERSON>ów przyjmuj<PERSON><PERSON><PERSON> fokus, gdy otwarte jest okno modalne.", "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, kt<PERSON><PERSON> mogą przy<PERSON><PERSON><PERSON><PERSON> fokus, nie są tabulowane w aktualnym stanie."}, "focusable-no-name": {"pass": "Element nie znajduje się w porządku tabulacji lub ma dostępny tekst.", "fail": "Element znajduje się w porządku tabulacji i nie ma dostępnego tekstu.", "incomplete": "<PERSON><PERSON> <PERSON>, czy <PERSON> ma dostępną nazwę."}, "focusable-not-tabbable": {"pass": "Nie ma elementów przyjmujących fokus wewnątrz elementu.", "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, na których można ustawić fokus, natychmiast otrzymują wskaźnik fokusu", "fail": "Treść przyjmująca fokus powinna mieć tabindex=-1 lub być usunięta z DOM."}, "frame-focusable-content": {"pass": "Element nie ma potomków przyjmujących fokus", "fail": "Element ma elementy potomne przyjmujące fokus", "incomplete": "<PERSON><PERSON> <PERSON>, czy element ma <PERSON>y potomne"}, "landmark-is-top-level": {"pass": "Punkt orientacyjny ${data.role} jest na najwyższym poziomie.", "fail": "<PERSON>t orientacyjny {data.role} jest wewną<PERSON>z innego obszaru kluczowego."}, "no-focusable-content": {"pass": "Element nie ma potomków przyjmujących fokus", "fail": {"default": "Element ma elementy potomne przyjmujące fokus", "notHidden": "Użycie ujemnej wartości tabindex  elementu wewnątrz interaktywnej kontrolki nie zapobiega ustawianiu na elemencie fokusu przez technologie wspomagające (nawet z 'aria-hidden=true')"}, "incomplete": "<PERSON><PERSON> <PERSON>, czy element ma <PERSON>y potomne"}, "page-has-heading-one": {"pass": "Strona ma co najmniej jeden nagłówek 1. poziomu.", "fail": "Strona musi mieć nagłówek 1. poziomu."}, "page-has-main": {"pass": "Dokument ma co najmniej jeden główny punkt orientacyjny.", "fail": "Dokument nie ma głównego punktu orientacyjnego."}, "page-no-duplicate-banner": {"pass": "W dokumencie nie ma więcej niż jeden obszar kluczowy banner.", "fail": "W dokumencie jest więcej niż jeden obszar kluczowy banner."}, "page-no-duplicate-contentinfo": {"pass": "W dokumencie nie ma więcej niż jeden obszar kluczowy contentinfo.", "fail": "W dokumencie jest więcej niż jeden obszar kluczowy contentinfo."}, "page-no-duplicate-main": {"pass": "W dokumencie nie ma więcej niż jeden obszar kluczowy main.", "fail": "W dokumencie jest więcej niż jeden obszar kluczowy main."}, "tabindex": {"pass": "Element nie ma wartości tabindex większej niż 0.", "fail": "Element nie ma wartości tabindex większą od 0."}, "alt-space-value": {"pass": "Element ma poprawną wartość atrybutu alt.", "fail": "Element ma atrybut alt zawierający tylko znak spacji, kt<PERSON>ry nie przez wszystkie czytniki ekranu jest ignorowany ."}, "duplicate-img-label": {"pass": "Element nie powiela tekstu istniejącego w atrybucie alt elementu img.", "fail": "Element powiela tekst istniejący w atrybucie alt elementu img."}, "explicit-label": {"pass": "Element formularza ma jawnie określoną label.", "fail": "Element formularza nie ma jawnie określonej label.", "incomplete": "<PERSON><PERSON> mo<PERSON>, czy element formularza ma jawnie określoną label."}, "help-same-as-label": {"pass": "<PERSON><PERSON><PERSON> pomocy (title lub aria-describedby) nie powiela tekstu etykiety.", "fail": "<PERSON><PERSON><PERSON> pomocy (title lub aria-describedby) jest taki sam jak tekst etykiety."}, "hidden-explicit-label": {"pass": "Element formularza ma widoczną jednoznaczną etykietę label.", "fail": "Element formularza ma jednoznaczną etykietę label, która jest ukryta.", "incomplete": "<PERSON><PERSON> m<PERSON>, czy element formularza ma <PERSON> et<PERSON> (label), gdy jest ukryta."}, "implicit-label": {"pass": "Element formularza ma dorozumianą etykietę (jest owinięty w label).", "fail": "Element formularza nie ma dorozumianej etykiety (nie jest owinięty w label).", "incomplete": "<PERSON><PERSON> m<PERSON>, czy element formularza ma dorozumianą etykietę (jest owinięty w label)."}, "label-content-name-mismatch": {"pass": "Widoczny tekst elementu jest częścią dostępnej nazwy elementu.", "fail": "Widoczny tekst wewnątrz elementu nie jest częścią dostępnej nazwy."}, "multiple-label": {"pass": "Pole formularza nie ma wielu label.", "incomplete": "Technologie wspomagające nie obsługują wystarczająco dobrze wielu elementów label. Upewnij si<PERSON>, że pierwsza etykieta zawiera wszystkie niezbędne informacje."}, "title-only": {"pass": "Element formularza nie używa wyłącznie atrybutu title jako swojej etykiety.", "fail": "Tylko atrybut title jest używany do wygenerowania etykiety dla elementu formularza."}, "landmark-is-unique": {"pass": "Punkty orientacyjne muszą mieć unikalną kombinację roli lub roli etykiety i tytułu (tj. dostępną nazwę).", "fail": "Punkt orientacyjny musi mieć unikalną aria-label, aria-labelledby lub title, aby był rozpoz<PERSON>wal<PERSON>."}, "has-lang": {"pass": "Element <html> ma atrybut lang.", "fail": {"noXHTML": "Atrybut xml:lang nie jest poprawny na stronach HTML, uż<PERSON>j atrybutu lang.", "noLang": "Element <html> nie ma atrybutu lang."}}, "valid-lang": {"pass": "Wartość atrybutu lang jest na liście poprawnych kodów języków.", "fail": "Wartości atrybutu lang nie ma na liście poprawnych kodów języków."}, "xml-lang-mismatch": {"pass": "Atrybuty lang i xml:lang mają ten sam język podstawowy.", "fail": "Atrybuty lang i xml:lang nie mają tego samego języka podstawowego."}, "dlitem": {"pass": "Element listy opisowej ma element nadrzędny dl.", "fail": "Pozycja listy opisowej nie ma elementu nadrzędnego dl."}, "listitem": {"pass": "Element listy ma ul, ol lub role=\"list\" jako bezpośredni element rodzicielski.", "fail": {"default": "Element listy nie ma nadrzędnego elementu ul lub ol.", "roleNotValid": "Element listy nie ma nadrzędnego elementu ul, ol, ani nadrzędnego elementu z role=\"list\"."}}, "only-dlitems": {"pass": "Element dl ma wewnątrz tylko dozwolone jako bezpośrednie elementy potomne (dzieci) elementy dt lub dd.", "fail": "Element listy ma wewnątrz bezpośrednie elementy dzieci, które nie są dozwolone wewnątrz listy opisowej."}, "only-listitems": {"pass": "Element lista ma wewnątrz tylko dozwolone jako bezpośrednie elementy potomne (dzieci) elementy li.", "fail": "Element listy ma bezpośrednie elementy potomne, które nie są dozwolone: ${data.values}"}, "structured-dlitems": {"pass": "<PERSON><PERSON> elemet dl nie jest pusty, ma zarówno elementy dt, jak i dd.", "fail": "Element nie jest pusty, ale nie ma co najmniej jednego elementu dt, po którym następuje co najmniej jeden element dd."}, "caption": {"pass": "Element multimedialny ma ścieżkę z napisami rozszerzonymi.", "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy istnieją napisy rozszerzone dla tego elementu."}, "frame-tested": {"pass": "Ramka iframe została przetestowana z axe-core.", "fail": "Ramka iframe nie mogła być przetestowana z axe-core.", "incomplete": "<PERSON><PERSON> wymagane jest przetestowanie ramki iframe z axe-core."}, "no-autoplay-audio": {"pass": "Element <video> lub <audio> nie emituje dźwięku przez czas dłuższy niż dozwolony lub posiada mechanizm sterujący.", "fail": "Element <video> lub <audio> emituje dźwięk przez czas dłuższy niż dozwolony i nie posiada mechanizmu sterowania.", "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy <video> lub <audio> nie emituje dźwięku dłuższej niż przez dozwolony czas lub czy ma mechanizm sterujący."}, "css-orientation-lock": {"pass": "Wyświetlacz działa sprawnie, orientacja nie jest blokowana.", "fail": "Zastosowano blokadę orientacji wyświetlacza, w rezultacie wyświetlacz nie działa sprawnie.", "incomplete": "Nie można blokować orientacji wyświetlacza za pomocą ustawień CSS."}, "meta-viewport-large": {"pass": "Znacznik <meta> nie ogranicza znacznego powiększenia na urządzeniach przenośnych.", "fail": "Znacznik <meta> znacznik ogranicza powiększanie na urządzeniach przenośnych."}, "meta-viewport": {"pass": "Znacznik <meta> nie wyłącza powiększania na urządzeniach przenośnych.", "fail": "${data} w znaczniku <meta> wyłącza powiększanie na urządzeniach przenośnych."}, "target-offset": {"pass": "Cel ma wystarczające odsunięcie od swojego najbliższego sąsiada: (${data.closestOffset}px, które powinno wyn<PERSON> co najmniej ${data.minOffset}px)", "fail": "Cel ma niewystarczające odsunięcie od najbliższego sąsiada: (${data.closestOffset}px, kt<PERSON>re powinn<PERSON> wyn<PERSON> co najmniej ${data.minOffset}px)", "incomplete": {"default": "Element z ujemnym tabindeksem ma niewystarczające odsunięcie od najbliższego sąsiada: (${data.closestOffset}px, kt<PERSON><PERSON> powinno wynosić co najmniej${data.minOffset}px). <PERSON>zy to jest cel?", "nonTabbableNeighbor": "Cel ma niewystarczające odsunięcie od sąsiada z ujemnym tabindeksem: (${data.closestOffset}px, kt<PERSON>re powinno wyn<PERSON>ić co najmniej ${data.minOffset}px). <PERSON><PERSON> sąsiad jest celem?"}}, "target-size": {"pass": {"default": "Kontrolka ma wystarczający rozmiar (${data.width}px na ${data.height}px, kt<PERSON><PERSON> p<PERSON><PERSON><PERSON> w<PERSON> co najmniej ${data.minSize}px na ${data.minSize}px)", "obscured": "Kontrolka jest i<PERSON>row<PERSON>, ponieważ jest całkowicie zasłonięta i nie można jej k<PERSON>nąć."}, "fail": {"default": "Cel ma niewystarczający rozmiar (${data.width}px na ${data.height}px, kt<PERSON><PERSON> p<PERSON><PERSON><PERSON> w<PERSON> co najmniej ${data.minSize}px na ${data.minSize}px)", "partiallyObscured": "Cel ma niewystarczający rozmiar, poni<PERSON><PERSON><PERSON> jest częściowo przesłonięty (najmniejsza przestrzeń to ${data.width}px na ${data.height}px, pow<PERSON>a wyn<PERSON> ${data.minSize}px na ${data.minSize}px)"}, "incomplete": {"default": "Element z ujemnym tabindex ma niewystarczający rozmiar (${data.width}px na ${data.height}px, p<PERSON><PERSON><PERSON> w<PERSON> co najmniej ${data.minSize}px na ${data.minSize}px). <PERSON>zy to jest cel?", "contentOverflow": "Rozmiar elementu nie mógł być dokładnie określony z powodu przepełnienia zawartości", "partiallyObscured": "Element z ujemnym tabindex ma niewystarczający rozmiar, poniew<PERSON>ż jest częściowo przesłonięty (najmniejsza przestrzeń to ${data.width}px na ${data.height}px, p<PERSON><PERSON><PERSON> co najmniej ${data.minSize}px na ${data.minSize}px). Czy to jest cel?", "partiallyObscuredNonTabbable": "Cel ma niewystarczający rozmiar, poni<PERSON><PERSON><PERSON> jest częściowo przesłonięty przez sąsiada o ujemnym tabindeksie (najmniejsza przestrzeń to ${data.width}px na ${data.height}px, p<PERSON><PERSON><PERSON> co najmniej ${data.minSize}px na ${data.minSize}px). <PERSON><PERSON> sąsiad jest celem?"}}, "header-present": {"pass": "Strona ma nagłówek.", "fail": "Strona nie ma nagłówka."}, "heading-order": {"pass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nagłówków jest poprawna.", "fail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nagłówków jest niepoprawna.", "incomplete": "Nie można określić poprzedniego nagłówka"}, "identical-links-same-purpose": {"pass": "Nie ma żadnych innych łączy o tej samej nazwie, które kierują na inny adres URL.", "incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy łącza mają ten sam cel lub są celowo niejednoznaczne."}, "internal-link-present": {"pass": "Znaleziono poprawne łącze pomijające.", "fail": "Nie znaleziono poprawnych łączy pomijających."}, "landmark": {"pass": "Strona ma punkt orientacyjny (obszar kluczowy).", "fail": "Strona nie ma żadnego punktu orientacyjnego (obszaru kluczowego)."}, "meta-refresh-no-exceptions": {"pass": "Znacznik <meta> nie powoduje natychmiastowego odświeżenia strony", "fail": "Znacznik <meta> tag wymusza czasowe odświeżenie strony"}, "meta-refresh": {"pass": "Znacznik <meta> nie odświeża od razu strony.", "fail": "Znacznik <meta> wymusza odświeżenie strony."}, "p-as-heading": {"pass": "Elementy <p> nie są stylizowane na nagłówki.", "fail": "Zamiast stylizowanych na nagłówki elementów <p> muszą być użyte nagłówki semantyczne.", "incomplete": "<PERSON><PERSON> m<PERSON>, czy <PERSON><PERSON> <p> są stylizowane na nagłówki"}, "region": {"pass": "Cała treść strony jest zawarta w obszarach kluczowych.", "fail": "Pewna c<PERSON> strony znajduje się poza punktami orientacyjnymi."}, "skip-link": {"pass": "Cel łącza pomijajacego istnieje.", "incomplete": "Cel łącza pomijania powinien być widoczny po aktywacji.", "fail": "<PERSON>e istnieje cel łącza pomijającego."}, "unique-frame-title": {"pass": "Atrybut title elementu jest unikalny.", "fail": "Atrybut title elementu nie jest unikalny."}, "duplicate-id-active": {"pass": "W dokumencie nie ma aktywnych elementów z takim samym atrybutem id.", "fail": "W dokumencie są aktywne elementy z tą samą wartością atrybutu id: ${data}."}, "duplicate-id-aria": {"pass": "W dokumencie nie ma żadnych elementów, do których odwołuje się ARIA lub <label>, które mają ten sam atrybut id.", "fail": "W dokumencie jest wiele elementów, do których odnosi się ARIA z tym samym atrybutem id: ${data}."}, "duplicate-id": {"pass": "W dokumencie nie ma wielu elementów statycznych z takim samym atrybutem id.", "fail": "W dokumencie jest wiele elementów statycznych z takim samym atrybutem id: ${data}."}, "aria-label": {"pass": "Atrybut aria-label istnieje i nie jest pusty.", "fail": "Atrybut aria-label nie istnieje lub jest pusty."}, "aria-labelledby": {"pass": "Atrybut aria-labelledby oraz elementy, do których się odwołuje, istnieją i są widoczne dla czytników ekranu.", "fail": "Atrybut aria-labelledby nie istnieje albo  elementy, do których odwołuje atrybut aria-labelledby, nie istnieją lub są puste.", "incomplete": "Spowoduj, aby istniał element, do którego istnieje odwołanie w atrybucie aria-labelldeby."}, "avoid-inline-spacing": {"pass": "Nie określono żadnych stylów wewnętrznych (inline) z '!important', które wpływają na odstępy w tekście.", "fail": {"singular": "Usuń dyrektywę '!important' ze stylu inline ${data.values}, ponieważ nadpisywanie tego nie jest obsługiwane przez wię<PERSON><PERSON><PERSON>ć przeglądarek.", "plural": "Usuń dyrektywy '!important' ze stylów inline ${data.values}, ponieważ nadpisywanie tego nie jest obsługiwane przez wię<PERSON><PERSON><PERSON>ć przeglądarek."}}, "button-has-visible-text": {"pass": "Element ma tekst wewnętrzny widoczny dla czytników ekranu.", "fail": "Element nie ma wewnę<PERSON><PERSON><PERSON><PERSON> te<PERSON>, kt<PERSON><PERSON> jest widoczny dla czytników ekranu.", "incomplete": "<PERSON><PERSON> m<PERSON>, czy element ma elementy potomne."}, "doc-has-title": {"pass": "Dokument ma niepusty element <title>.", "fail": "Dokument nie ma niepustego elementu <title>."}, "exists": {"pass": "Element nie istnieje.", "incomplete": "Element istnieje."}, "has-alt": {"pass": "Element ma atrybut alt.", "fail": "Element nie ma atrybutu alt."}, "has-visible-text": {"pass": "Element ma tekst, kt<PERSON>ry jest widoczny dla czytników ekranu.", "fail": "Element nie ma tekstu, kt<PERSON><PERSON> jest widoczny dla czytników ekranu.", "incomplete": "<PERSON><PERSON> m<PERSON>, czy element ma <PERSON>y d<PERSON>ci."}, "important-letter-spacing": {"pass": "Odstępy między literami (letter-spacing) w atrybucie style nie są ustawione na !important lub spełniają minimum", "fail": "Odstępy między literami w atrybucie style nie mogą używać !important lub muszą mieć ${data.minValue}em (aktualnie: ${data.value}em)"}, "important-line-height": {"pass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> line-height w atrybucie style nie jest ustawiona na !important lub spełnia minimum.", "fail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> line-height w atrybucie style nie może używać !important lub musi mieć ${data.minValue}em (aktualnie: ${data.value}em)."}, "important-word-spacing": {"pass": "Odstępy miedzy w<PERSON>i (word-spacing) w atrybucie style nie są ustawione na !important lub spełniają minimum", "fail": "Odstępy miedzy w<PERSON> (word-spacing) w atrybucie style nie mogą uż<PERSON>wać !important lub muszą mieć ${data.minValue}em (aktualnie: ${data.value}em)"}, "is-on-screen": {"pass": "Element nie jest widoczny.", "fail": "Element jest widoczny."}, "non-empty-alt": {"pass": "Element ma niepusty atrybut alt.", "fail": {"noAttr": "Element nie ma atrybutu alt.", "emptyAttr": "Element ma pusty atrybut alt."}}, "non-empty-if-present": {"pass": {"default": "Element nie posiada atrybutu bez określonej wartości.", "has-label": "Element ma atrybuty bez określonej wartości."}, "fail": "Element ma atrybut warto<PERSON>ci, a atrybut warto<PERSON>ci jest pusty."}, "non-empty-placeholder": {"pass": "Element ma atrybut placeholder.", "fail": {"noAttr": "Element nie ma atrybutu placeholder.", "emptyAttr": "Element ma pusty atrybut placeholder."}}, "non-empty-title": {"pass": "Element ma atrybut title.", "fail": {"noAttr": "Element nie ma atrybutu title.", "emptyAttr": "Element ma pusty atrybut title."}}, "non-empty-value": {"pass": "Element ma atrybut z niepustą wartością.", "fail": {"noAttr": "Element nie ma atrybutu z wartością.", "emptyAttr": "Element ma atrybut z pustą wartością."}}, "presentational-role": {"pass": "Domyślna semantyka elementu została zastąpiona przez role=\"${data.role}\".", "fail": {"default": "Domyślna semantyka elementu nie została nadpisana przez role=\"none\" ani role=\"presentation\".", "globalAria": "Rola elementu nie jest prezentacyjna, ponieważ ma on ogólny atrybut ARIA.", "focusable": "Rola elementu nie jest prezentacyjna, poni<PERSON><PERSON>ż może on przyjmować fokus.", "both": "Rola elementu nie jest prezentacyjna, ponieważ ma on ogólny atrybut ARIA i może przyjmować fokus.", "iframe": "Użycie atrybutu \"title\" na elemencie ${data.nodeName} z rolą prezentacyjną zachowuje się niespójnie pomiędzy czytnikami ekranu."}}, "role-none": {"pass": "Domyślna semantyka elementu została zastąpiona przez role=\"none\".", "fail": "Domyślna semantyka elementu nie została nadpisana przez role=\"none\"."}, "role-presentation": {"pass": "Domyślna semantyka elementu została zastąpiona przez role=\"presentation\".", "fail": "Domyślna semantyka elementu nie została nadpisana przez role=\"presentation\"."}, "svg-non-empty-title": {"pass": "Element ma dziecko, kt<PERSON>re jest tytułem.", "fail": {"noTitle": "Element nie ma d<PERSON>cka, kt<PERSON>re jest tytułem.", "emptyTitle": "Element title dziecka jest pusty."}, "incomplete": "<PERSON><PERSON> <PERSON>, czy <PERSON> ma <PERSON>, kt<PERSON>re jest tytułem."}, "caption-faked": {"pass": "<PERSON><PERSON><PERSON> wiersz tabeli nie jest używany jako pod<PERSON>.", "fail": "Pierwszym elementem potomnym (dzieckiem) w tabeli powinien być caption zamiast komórki tabeli."}, "html5-scope": {"pass": "Atrybut scope jest używany tylko w elementach nagłówków tabeli (<th>).", "fail": "W HTML 5, atrybuty scope mogą być używane tylko w elementach nagłówkowych tabeli (<th>)."}, "same-caption-summary": {"pass": "Treści atrybutu summary i elementu <caption> nie są powielane.", "fail": "Treści atrybutu summary i elementu <caption> są identyczne.", "incomplete": "<PERSON><PERSON> m<PERSON>, czy element <table> ma caption"}, "scope-value": {"pass": "Atrybut scope ma poprawną wartość.", "fail": "Wartością atrybutu scope może być tylko 'row' lub 'col'."}, "td-has-header": {"pass": "Wszystkie niepuste komórki danych mają nagłówki tabeli.", "fail": "Niektóre niepuste komórki danych nie mają nagłówków tabeli."}, "td-headers-attr": {"pass": "Atrybut headers jest używany wyłącznie w odniesieniu do innych komórek w tabeli.", "incomplete": "Atrybut headers jest pusty.", "fail": "Atrybut headers nie jest używany wyłącznie w odniesieniu do innych komórek w tabeli."}, "th-has-data-cells": {"pass": "Wszystkie komórki nagłówkowe tabeli odnoszą się do komórek danych.", "fail": "Nie wszystkie komórki nagłówkowe tabeli odnoszą się do komórek danych.", "incomplete": "Komórki danych tabeli są puste lub nie istnieją."}, "hidden-content": {"pass": "Wszystkie treści na stronie zostały przeanalizowane.", "fail": "Były problemy z analizą treści na tej stronie.", "incomplete": "Na tej stronie jest ukry<PERSON> tre<PERSON>, która nie została przeanalizowana. <PERSON><PERSON><PERSON> wyświetlanie tej treści, a<PERSON> j<PERSON> p<PERSON>."}}, "failureSummaries": {"any": {"failureMessage": "Napraw następujące elementy: {{~it:value}}\n {{=value.split('\\n').join('\\n ')}}{{~}}"}, "none": {"failureMessage": "Napraw wszystkie poniższe: {{~it:value}}\n {{=value.split('\\n').join('\\n ')}}{{~}}"}}, "incompleteFallbackMessage": "AXE nie potrafił określić powodu. <PERSON>zas użyć inspektora elementów!"}