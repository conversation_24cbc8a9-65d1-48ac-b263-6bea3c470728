{"version": 3, "file": "prefer-regexp-exec.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-regexp-exec.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AAGnC,kCAMiB;AAEjB,IAAK,YAKJ;AALD,WAAK,YAAY;IACf,iDAAS,CAAA;IACT,mDAAe,CAAA;IACf,mDAAe,CAAA;IACf,+CAAsB,CAAA;AACxB,CAAC,EALI,YAAY,KAAZ,YAAY,QAKhB;AAED,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,oBAAoB;IAC1B,cAAc,EAAE,EAAE;IAElB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,MAAM;QACf,IAAI,EAAE;YACJ,WAAW,EACT,yEAAyE;YAC3E,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,yBAAyB,EAAE,yCAAyC;SACrE;QACD,MAAM,EAAE,EAAE;KACX;IAED,MAAM,CAAC,OAAO;QACZ,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QACvC,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAC5D,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAa;YACjC,OAAO,IAAA,kBAAW,EAAC,WAAW,EAAE,IAAI,CAAC,KAAK,QAAQ,CAAC;QACrD,CAAC;QAED;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAa;YACjC,OAAO,IAAA,kBAAW,EAAC,WAAW,EAAE,IAAI,CAAC,KAAK,QAAQ,CAAC;QACrD,CAAC;QAED,SAAS,oBAAoB,CAAC,KAAgB;YAC5C,IAAI,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;YAEhC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;oBACtB,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC;iBAC/B;qBAAM,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;oBAC7B,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC;iBAC/B;aACF;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,SAAS,2BAA2B,CAClC,IAAqC;YAErC,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;gBAC3C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAC1C;gBACA,MAAM,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;gBACjC,OAAO,CACL,KAAK;oBACL,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACrC,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;oBAC/B,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC1B,CAAC;aACH;YAED,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,CAAC;QACjD,CAAC;QAED,OAAO;YACL,qGAAqG,CACnG,UAAqC;gBAErC,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;gBACrC,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAiC,CAAC;gBAC9D,MAAM,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;gBAC1C,MAAM,aAAa,GAAG,IAAA,qBAAc,EAAC,YAAY,EAAE,WAAW,CAAC,CAAC;gBAEhE,IACE,CAAC,YAAY,CACX,WAAW,CAAC,iBAAiB,CAC3B,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CACrD,CACF,EACD;oBACA,OAAO;iBACR;gBAED,qDAAqD;gBACrD,IACE,CAAC,CAAC,aAAa,IAAI,2BAA2B,CAAC,YAAY,CAAC,CAAC;oBAC7D,CAAC,aAAa;wBACZ,aAAa,CAAC,KAAK,YAAY,MAAM;wBACrC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAC1C;oBACA,OAAO;iBACR;gBAED,IACE,YAAY,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBAC5C,OAAO,YAAY,CAAC,KAAK,KAAK,QAAQ,EACtC;oBACA,IAAI,MAAc,CAAC;oBACnB,IAAI;wBACF,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;qBACrC;oBAAC,WAAM;wBACN,OAAO;qBACR;oBACD,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI,EAAE,UAAU,CAAC,QAAQ;wBACzB,SAAS,EAAE,2BAA2B;wBACtC,GAAG,EAAE,IAAA,uBAAgB,EAAC;4BACpB,UAAU;4BACV,IAAI,EAAE,QAAQ;4BACd,SAAS,EAAE,CAAC,UAAU,CAAC;4BACvB,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,SAAS,UAAU,GAAG;yBAC/D,CAAC;qBACH,CAAC,CAAC;iBACJ;gBAED,MAAM,YAAY,GAAG,WAAW,CAAC,iBAAiB,CAChD,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CACvD,CAAC;gBACF,MAAM,aAAa,GAAG,oBAAoB,CACxC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,CACrC,CAAC;gBACF,QAAQ,aAAa,EAAE;oBACrB,KAAK,YAAY,CAAC,MAAM;wBACtB,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI,EAAE,UAAU,CAAC,QAAQ;4BACzB,SAAS,EAAE,2BAA2B;4BACtC,GAAG,EAAE,IAAA,uBAAgB,EAAC;gCACpB,UAAU;gCACV,IAAI,EAAE,QAAQ;gCACd,SAAS,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;gCACrC,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,CACjC,GAAG,YAAY,SAAS,UAAU,GAAG;6BACxC,CAAC;yBACH,CAAC,CAAC;oBAEL,KAAK,YAAY,CAAC,MAAM;wBACtB,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI,EAAE,UAAU,CAAC,QAAQ;4BACzB,SAAS,EAAE,2BAA2B;4BACtC,GAAG,EAAE,IAAA,uBAAgB,EAAC;gCACpB,UAAU;gCACV,IAAI,EAAE,QAAQ;gCACd,SAAS,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;gCACrC,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,CACjC,UAAU,YAAY,UAAU,UAAU,GAAG;6BAChD,CAAC;yBACH,CAAC,CAAC;iBACN;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}